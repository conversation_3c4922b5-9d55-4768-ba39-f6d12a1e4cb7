#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WLAN-ESS接口配置提取和对比工具
用于从多个txt配置文件中提取WLAN-ESS接口配置，并对相同接口名进行对比分析
"""

import os
import re
import json
from collections import defaultdict
from typing import Dict, List, Tuple, Set
import argparse
import pandas as pd
from datetime import datetime


class WLANInterfaceExtractor:
    def __init__(self):
        self.interface_pattern = re.compile(r'^interface\s+(WLAN-ESS\d+)', re.IGNORECASE)
        self.config_end_pattern = re.compile(r'^#\s*$')
        
    def extract_interfaces_from_file(self, file_path: str) -> Dict[str, Dict]:
        """
        从单个文件中提取所有WLAN-ESS接口配置
        
        Args:
            file_path: 配置文件路径
            
        Returns:
            字典，键为接口名，值为接口配置信息
        """
        interfaces = {}
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
        except Exception as e:
            print(f"读取文件 {file_path} 失败: {e}")
            return interfaces
            
        current_interface = None
        current_config = []
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            
            # 检查是否是WLAN-ESS接口开始
            interface_match = self.interface_pattern.match(line)
            if interface_match:
                # 保存之前的接口配置
                if current_interface and current_config:
                    interfaces[current_interface] = self._parse_interface_config(
                        current_interface, current_config, file_path
                    )
                
                # 开始新接口
                current_interface = interface_match.group(1)
                current_config = [line]
                continue
            
            # 如果当前在处理接口配置
            if current_interface:
                current_config.append(line)
                
                # 检查配置是否结束
                if self.config_end_pattern.match(line):
                    interfaces[current_interface] = self._parse_interface_config(
                        current_interface, current_config, file_path
                    )
                    current_interface = None
                    current_config = []
        
        # 处理文件末尾的接口（如果没有#结尾）
        if current_interface and current_config:
            interfaces[current_interface] = self._parse_interface_config(
                current_interface, current_config, file_path
            )
        
        return interfaces
    
    def _parse_interface_config(self, interface_name: str, config_lines: List[str], file_path: str) -> Dict:
        """
        解析单个接口的配置信息
        
        Args:
            interface_name: 接口名称
            config_lines: 配置行列表
            file_path: 文件路径
            
        Returns:
            解析后的配置字典
        """
        config = {
            'interface_name': interface_name,
            'source_file': os.path.basename(file_path),
            'raw_config': config_lines,
            'description': '',
            'port_type': '',
            'vlan_config': {},
            'security_config': {},
            'other_config': []
        }
        
        for line in config_lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            # 解析描述
            if line.startswith('description '):
                config['description'] = line.replace('description ', '')
            
            # 解析端口类型
            elif line.startswith('port link-type '):
                config['port_type'] = line.replace('port link-type ', '')
            
            # 解析VLAN配置
            elif 'vlan' in line.lower():
                if 'undo port hybrid vlan' in line:
                    config['vlan_config']['undo_vlan'] = line.replace('undo port hybrid vlan ', '')
                elif 'port hybrid vlan' in line and 'untagged' in line:
                    vlan_match = re.search(r'port hybrid vlan (\d+) untagged', line)
                    if vlan_match:
                        config['vlan_config']['untagged_vlan'] = vlan_match.group(1)
                elif 'port hybrid pvid vlan' in line:
                    vlan_match = re.search(r'port hybrid pvid vlan (\d+)', line)
                    if vlan_match:
                        config['vlan_config']['pvid_vlan'] = vlan_match.group(1)
                elif 'mac-vlan enable' in line:
                    config['vlan_config']['mac_vlan'] = 'enable'
            
            # 解析安全配置
            elif 'security' in line.lower() or 'dot1x' in line.lower():
                if 'port-security port-mode' in line:
                    config['security_config']['port_mode'] = line.replace('port-security port-mode ', '')
                elif 'port-security tx-key-type' in line:
                    config['security_config']['tx_key_type'] = line.replace('port-security tx-key-type ', '')
                elif 'undo dot1x handshake' in line:
                    config['security_config']['dot1x_handshake'] = 'disabled'
                elif 'dot1x mandatory-domain' in line:
                    config['security_config']['dot1x_domain'] = line.replace('dot1x mandatory-domain ', '')
            
            # 其他配置
            elif not line.startswith('interface '):
                config['other_config'].append(line)
        
        return config
    
    def extract_from_directory(self, directory: str) -> Dict[str, Dict[str, Dict]]:
        """
        从目录中的所有txt文件提取接口配置
        
        Args:
            directory: 目录路径
            
        Returns:
            字典，键为文件名，值为该文件的接口配置字典
        """
        all_interfaces = {}
        
        for filename in os.listdir(directory):
            if filename.lower().endswith('.txt'):
                file_path = os.path.join(directory, filename)
                print(f"正在处理文件: {filename}")
                interfaces = self.extract_interfaces_from_file(file_path)
                if interfaces:
                    all_interfaces[filename] = interfaces
                    print(f"  找到 {len(interfaces)} 个WLAN-ESS接口")
                else:
                    print(f"  未找到WLAN-ESS接口")
        
        return all_interfaces
    
    def compare_interfaces(self, all_interfaces: Dict[str, Dict[str, Dict]]) -> Dict[str, List]:
        """
        对比相同接口名的配置差异
        
        Args:
            all_interfaces: 所有文件的接口配置
            
        Returns:
            对比结果字典
        """
        # 收集所有接口名
        interface_names = set()
        for file_interfaces in all_interfaces.values():
            interface_names.update(file_interfaces.keys())
        
        comparison_results = {}
        
        for interface_name in sorted(interface_names):
            # 找到包含此接口的所有文件
            files_with_interface = []
            for filename, file_interfaces in all_interfaces.items():
                if interface_name in file_interfaces:
                    files_with_interface.append((filename, file_interfaces[interface_name]))
            
            if len(files_with_interface) > 1:
                comparison_results[interface_name] = self._compare_interface_configs(
                    interface_name, files_with_interface
                )
        
        return comparison_results

    def _compare_interface_configs(self, interface_name: str, files_with_interface: List[Tuple[str, Dict]]) -> Dict:
        """
        对比单个接口在不同文件中的配置差异

        Args:
            interface_name: 接口名称
            files_with_interface: 包含该接口的文件列表，每个元素为(文件名, 配置字典)

        Returns:
            对比结果字典
        """
        comparison = {
            'interface_name': interface_name,
            'files': [f[0] for f in files_with_interface],
            'differences': {},
            'identical': True
        }

        # 对比各个配置项
        config_keys = ['description', 'port_type', 'vlan_config', 'security_config']

        for key in config_keys:
            values = {}
            for filename, config in files_with_interface:
                values[filename] = config.get(key, '')

            # 检查是否所有值都相同
            unique_values = set(str(v) for v in values.values())
            if len(unique_values) > 1:
                comparison['differences'][key] = values
                comparison['identical'] = False

        return comparison

    def save_results_to_json(self, all_interfaces: Dict, comparison_results: Dict, output_file: str):
        """
        将结果保存为JSON文件

        Args:
            all_interfaces: 所有接口配置
            comparison_results: 对比结果
            output_file: 输出文件路径
        """
        results = {
            'extraction_results': all_interfaces,
            'comparison_results': comparison_results,
            'summary': {
                'total_files': len(all_interfaces),
                'total_interfaces': sum(len(interfaces) for interfaces in all_interfaces.values()),
                'interfaces_with_differences': len(comparison_results)
            }
        }

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"结果已保存到: {output_file}")

    def save_results_to_excel(self, all_interfaces: Dict, comparison_results: Dict, output_file: str):
        """
        将结果保存为Excel文件

        Args:
            all_interfaces: 所有接口配置
            comparison_results: 对比结果
            output_file: 输出文件路径
        """
        # 创建Excel写入器
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 1. 创建接口配置汇总表
            self._create_interface_summary_sheet(all_interfaces, writer)

            # 2. 创建详细配置表
            self._create_detailed_config_sheet(all_interfaces, writer)

            # 3. 创建VLAN配置对比表
            self._create_vlan_comparison_sheet(all_interfaces, writer)

            # 4. 创建安全配置对比表
            self._create_security_comparison_sheet(all_interfaces, writer)

            # 5. 创建配置差异表
            if comparison_results:
                self._create_differences_sheet(comparison_results, writer)

            # 6. 创建统计摘要表
            self._create_summary_sheet(all_interfaces, comparison_results, writer)

        print(f"Excel结果已保存到: {output_file}")

    def _create_interface_summary_sheet(self, all_interfaces: Dict, writer):
        """创建接口配置汇总表"""
        data = []

        for filename, interfaces in all_interfaces.items():
            for interface_name, config in interfaces.items():
                # 处理VLAN配置
                vlan_info = config.get('vlan_config', {})
                untagged_vlan = vlan_info.get('untagged_vlan', '')
                pvid_vlan = vlan_info.get('pvid_vlan', '')

                # 处理安全配置
                security_info = config.get('security_config', {})
                has_security = 'Yes' if security_info else 'No'
                port_mode = security_info.get('port_mode', '')

                data.append({
                    '源文件': filename,
                    '接口名称': interface_name,
                    '描述': config.get('description', ''),
                    '端口类型': config.get('port_type', ''),
                    'Untagged VLAN': untagged_vlan,
                    'PVID VLAN': pvid_vlan,
                    'MAC-VLAN': vlan_info.get('mac_vlan', ''),
                    '安全配置': has_security,
                    '端口安全模式': port_mode,
                    'DOT1X域': security_info.get('dot1x_domain', '')
                })

        df = pd.DataFrame(data)
        df.to_excel(writer, sheet_name='接口配置汇总', index=False)

        # 设置列宽
        worksheet = writer.sheets['接口配置汇总']
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width

    def _create_detailed_config_sheet(self, all_interfaces: Dict, writer):
        """创建详细配置表"""
        data = []

        for filename, interfaces in all_interfaces.items():
            for interface_name, config in interfaces.items():
                # 将原始配置转换为字符串
                raw_config = '\n'.join(config.get('raw_config', []))

                data.append({
                    '源文件': filename,
                    '接口名称': interface_name,
                    '描述': config.get('description', ''),
                    '端口类型': config.get('port_type', ''),
                    '原始配置': raw_config
                })

        df = pd.DataFrame(data)
        df.to_excel(writer, sheet_name='详细配置', index=False)

        # 设置列宽和行高
        worksheet = writer.sheets['详细配置']
        worksheet.column_dimensions['E'].width = 80  # 原始配置列
        for column in ['A', 'B', 'C', 'D']:
            worksheet.column_dimensions[column].width = 20

    def _create_vlan_comparison_sheet(self, all_interfaces: Dict, writer):
        """创建VLAN配置对比表"""
        data = []

        # 收集所有接口名
        all_interface_names = set()
        for interfaces in all_interfaces.values():
            all_interface_names.update(interfaces.keys())

        for interface_name in sorted(all_interface_names):
            row = {'接口名称': interface_name}

            for filename, interfaces in all_interfaces.items():
                if interface_name in interfaces:
                    config = interfaces[interface_name]
                    vlan_config = config.get('vlan_config', {})

                    vlan_summary = []
                    if vlan_config.get('untagged_vlan'):
                        vlan_summary.append(f"Untagged: {vlan_config['untagged_vlan']}")
                    if vlan_config.get('pvid_vlan'):
                        vlan_summary.append(f"PVID: {vlan_config['pvid_vlan']}")
                    if vlan_config.get('mac_vlan'):
                        vlan_summary.append(f"MAC-VLAN: {vlan_config['mac_vlan']}")

                    row[filename] = '; '.join(vlan_summary) if vlan_summary else '无VLAN配置'
                else:
                    row[filename] = '接口不存在'

            data.append(row)

        df = pd.DataFrame(data)
        df.to_excel(writer, sheet_name='VLAN配置对比', index=False)

        # 设置列宽
        worksheet = writer.sheets['VLAN配置对比']
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 60)
            worksheet.column_dimensions[column_letter].width = adjusted_width

    def print_summary(self, all_interfaces: Dict, comparison_results: Dict):
        """
        打印摘要信息

        Args:
            all_interfaces: 所有接口配置
            comparison_results: 对比结果
        """
        print("\n" + "="*60)
        print("WLAN-ESS接口配置提取和对比结果摘要")
        print("="*60)

        # 文件统计
        print(f"\n处理的文件数量: {len(all_interfaces)}")
        for filename, interfaces in all_interfaces.items():
            print(f"  {filename}: {len(interfaces)} 个接口")

        # 接口统计
        all_interface_names = set()
        for interfaces in all_interfaces.values():
            all_interface_names.update(interfaces.keys())

        print(f"\n发现的接口总数: {len(all_interface_names)}")
        print("接口列表:", sorted(all_interface_names))

        # 对比结果
        if comparison_results:
            print(f"\n存在配置差异的接口数量: {len(comparison_results)}")
            for interface_name, comparison in comparison_results.items():
                print(f"\n接口 {interface_name}:")
                print(f"  出现在文件: {', '.join(comparison['files'])}")
                if comparison['differences']:
                    print("  配置差异:")
                    for key, values in comparison['differences'].items():
                        print(f"    {key}:")
                        for filename, value in values.items():
                            print(f"      {filename}: {value}")
        else:
            print("\n所有相同名称的接口配置都一致")

    def print_detailed_config(self, all_interfaces: Dict):
        """
        打印详细的接口配置信息

        Args:
            all_interfaces: 所有接口配置
        """
        print("\n" + "="*60)
        print("详细接口配置信息")
        print("="*60)

        for filename, interfaces in all_interfaces.items():
            print(f"\n文件: {filename}")
            print("-" * 40)

            for interface_name, config in interfaces.items():
                print(f"\n接口: {interface_name}")
                if config['description']:
                    print(f"  描述: {config['description']}")
                if config['port_type']:
                    print(f"  端口类型: {config['port_type']}")

                if config['vlan_config']:
                    print("  VLAN配置:")
                    for key, value in config['vlan_config'].items():
                        print(f"    {key}: {value}")

                if config['security_config']:
                    print("  安全配置:")
                    for key, value in config['security_config'].items():
                        print(f"    {key}: {value}")

                if config['other_config']:
                    print("  其他配置:")
                    for line in config['other_config']:
                        if line.strip():
                            print(f"    {line}")

                print("  原始配置:")
                for line in config['raw_config']:
                    if line.strip():
                        print(f"    {line}")


def main():
    parser = argparse.ArgumentParser(description='WLAN-ESS接口配置提取和对比工具')
    parser.add_argument('directory', nargs='?', default='.',
                       help='包含txt配置文件的目录路径 (默认: 当前目录)')
    parser.add_argument('-o', '--output', default='wlan_interfaces_result.json',
                       help='输出JSON文件路径 (默认: wlan_interfaces_result.json)')
    parser.add_argument('-d', '--detailed', action='store_true',
                       help='显示详细的接口配置信息')
    parser.add_argument('-c', '--compare-only', action='store_true',
                       help='只显示有差异的接口对比结果')

    args = parser.parse_args()

    # 创建提取器实例
    extractor = WLANInterfaceExtractor()

    # 提取接口配置
    print(f"开始从目录 '{args.directory}' 提取WLAN-ESS接口配置...")
    all_interfaces = extractor.extract_from_directory(args.directory)

    if not all_interfaces:
        print("未找到任何包含WLAN-ESS接口的txt文件")
        return

    # 对比接口配置
    print("\n开始对比相同接口名的配置...")
    comparison_results = extractor.compare_interfaces(all_interfaces)

    # 保存结果到JSON文件
    extractor.save_results_to_json(all_interfaces, comparison_results, args.output)

    # 显示结果
    if args.compare_only and comparison_results:
        print("\n存在配置差异的接口:")
        for interface_name, comparison in comparison_results.items():
            print(f"\n接口 {interface_name}:")
            print(f"  出现在文件: {', '.join(comparison['files'])}")
            if comparison['differences']:
                print("  配置差异:")
                for key, values in comparison['differences'].items():
                    print(f"    {key}:")
                    for filename, value in values.items():
                        print(f"      {filename}: {value}")
    elif args.detailed:
        extractor.print_detailed_config(all_interfaces)
        extractor.print_summary(all_interfaces, comparison_results)
    else:
        extractor.print_summary(all_interfaces, comparison_results)


if __name__ == '__main__':
    main()
