#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WLAN-ESS接口配置提取和对比工具
用于从多个txt配置文件中提取WLAN-ESS接口配置，并对相同接口名进行对比分析
"""

import os
import re
import json
from collections import defaultdict
from typing import Dict, List, Tuple, Set
import argparse


class WLANInterfaceExtractor:
    def __init__(self):
        self.interface_pattern = re.compile(r'^interface\s+(WLAN-ESS\d+)', re.IGNORECASE)
        self.config_end_pattern = re.compile(r'^#\s*$')
        
    def extract_interfaces_from_file(self, file_path: str) -> Dict[str, Dict]:
        """
        从单个文件中提取所有WLAN-ESS接口配置
        
        Args:
            file_path: 配置文件路径
            
        Returns:
            字典，键为接口名，值为接口配置信息
        """
        interfaces = {}
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
        except Exception as e:
            print(f"读取文件 {file_path} 失败: {e}")
            return interfaces
            
        current_interface = None
        current_config = []
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            
            # 检查是否是WLAN-ESS接口开始
            interface_match = self.interface_pattern.match(line)
            if interface_match:
                # 保存之前的接口配置
                if current_interface and current_config:
                    interfaces[current_interface] = self._parse_interface_config(
                        current_interface, current_config, file_path
                    )
                
                # 开始新接口
                current_interface = interface_match.group(1)
                current_config = [line]
                continue
            
            # 如果当前在处理接口配置
            if current_interface:
                current_config.append(line)
                
                # 检查配置是否结束
                if self.config_end_pattern.match(line):
                    interfaces[current_interface] = self._parse_interface_config(
                        current_interface, current_config, file_path
                    )
                    current_interface = None
                    current_config = []
        
        # 处理文件末尾的接口（如果没有#结尾）
        if current_interface and current_config:
            interfaces[current_interface] = self._parse_interface_config(
                current_interface, current_config, file_path
            )
        
        return interfaces
    
    def _parse_interface_config(self, interface_name: str, config_lines: List[str], file_path: str) -> Dict:
        """
        解析单个接口的配置信息
        
        Args:
            interface_name: 接口名称
            config_lines: 配置行列表
            file_path: 文件路径
            
        Returns:
            解析后的配置字典
        """
        config = {
            'interface_name': interface_name,
            'source_file': os.path.basename(file_path),
            'raw_config': config_lines,
            'description': '',
            'port_type': '',
            'vlan_config': {},
            'security_config': {},
            'other_config': []
        }
        
        for line in config_lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            # 解析描述
            if line.startswith('description '):
                config['description'] = line.replace('description ', '')
            
            # 解析端口类型
            elif line.startswith('port link-type '):
                config['port_type'] = line.replace('port link-type ', '')
            
            # 解析VLAN配置
            elif 'vlan' in line.lower():
                if 'undo port hybrid vlan' in line:
                    config['vlan_config']['undo_vlan'] = line.replace('undo port hybrid vlan ', '')
                elif 'port hybrid vlan' in line and 'untagged' in line:
                    vlan_match = re.search(r'port hybrid vlan (\d+) untagged', line)
                    if vlan_match:
                        config['vlan_config']['untagged_vlan'] = vlan_match.group(1)
                elif 'port hybrid pvid vlan' in line:
                    vlan_match = re.search(r'port hybrid pvid vlan (\d+)', line)
                    if vlan_match:
                        config['vlan_config']['pvid_vlan'] = vlan_match.group(1)
                elif 'mac-vlan enable' in line:
                    config['vlan_config']['mac_vlan'] = 'enable'
            
            # 解析安全配置
            elif 'security' in line.lower() or 'dot1x' in line.lower():
                if 'port-security port-mode' in line:
                    config['security_config']['port_mode'] = line.replace('port-security port-mode ', '')
                elif 'port-security tx-key-type' in line:
                    config['security_config']['tx_key_type'] = line.replace('port-security tx-key-type ', '')
                elif 'undo dot1x handshake' in line:
                    config['security_config']['dot1x_handshake'] = 'disabled'
                elif 'dot1x mandatory-domain' in line:
                    config['security_config']['dot1x_domain'] = line.replace('dot1x mandatory-domain ', '')
            
            # 其他配置
            elif not line.startswith('interface '):
                config['other_config'].append(line)
        
        return config
    
    def extract_from_directory(self, directory: str) -> Dict[str, Dict[str, Dict]]:
        """
        从目录中的所有txt文件提取接口配置
        
        Args:
            directory: 目录路径
            
        Returns:
            字典，键为文件名，值为该文件的接口配置字典
        """
        all_interfaces = {}
        
        for filename in os.listdir(directory):
            if filename.lower().endswith('.txt'):
                file_path = os.path.join(directory, filename)
                print(f"正在处理文件: {filename}")
                interfaces = self.extract_interfaces_from_file(file_path)
                if interfaces:
                    all_interfaces[filename] = interfaces
                    print(f"  找到 {len(interfaces)} 个WLAN-ESS接口")
                else:
                    print(f"  未找到WLAN-ESS接口")
        
        return all_interfaces
    
    def compare_interfaces(self, all_interfaces: Dict[str, Dict[str, Dict]]) -> Dict[str, List]:
        """
        对比相同接口名的配置差异
        
        Args:
            all_interfaces: 所有文件的接口配置
            
        Returns:
            对比结果字典
        """
        # 收集所有接口名
        interface_names = set()
        for file_interfaces in all_interfaces.values():
            interface_names.update(file_interfaces.keys())
        
        comparison_results = {}
        
        for interface_name in sorted(interface_names):
            # 找到包含此接口的所有文件
            files_with_interface = []
            for filename, file_interfaces in all_interfaces.items():
                if interface_name in file_interfaces:
                    files_with_interface.append((filename, file_interfaces[interface_name]))
            
            if len(files_with_interface) > 1:
                comparison_results[interface_name] = self._compare_interface_configs(
                    interface_name, files_with_interface
                )
        
        return comparison_results
