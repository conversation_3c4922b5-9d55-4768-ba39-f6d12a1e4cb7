{"extraction_results": {"*************-Default--20240830-093435.txt": {"WLAN-ESS1": {"interface_name": "WLAN-ESS1", "source_file": "*************-De<PERSON><PERSON>--20240830-093435.txt", "raw_config": ["interface WLAN-ESS1", "description yewu", "port link-type hybrid", "undo port hybrid vlan 1", "port hybrid vlan 3321 untagged", "port hybrid pvid vlan 3321", "mac-vlan enable", "#"], "description": "yewu", "port_type": "hybrid", "vlan_config": {"undo_vlan": "1", "untagged_vlan": "3321", "pvid_vlan": "3321", "mac_vlan": "enable"}, "security_config": {}, "other_config": []}, "WLAN-ESS15": {"interface_name": "WLAN-ESS15", "source_file": "*************-De<PERSON><PERSON>--20240830-093435.txt", "raw_config": ["interface WLAN-ESS15", "description CMCC-AUTO", "port link-type hybrid", "undo port hybrid vlan 1", "port hybrid vlan 3321 untagged", "port hybrid pvid vlan 3321", "mac-vlan enable", "port-security port-mode userlogin-secure-ext", "port-security tx-key-type 11key", "undo dot1x handshake", "dot1x mandatory-domain eap", "#"], "description": "CMCC-AUTO", "port_type": "hybrid", "vlan_config": {"undo_vlan": "1", "untagged_vlan": "3321", "pvid_vlan": "3321", "mac_vlan": "enable"}, "security_config": {"port_mode": "userlogin-secure-ext", "tx_key_type": "11key", "dot1x_handshake": "disabled", "dot1x_domain": "eap"}, "other_config": []}, "WLAN-ESS66": {"interface_name": "WLAN-ESS66", "source_file": "*************-De<PERSON><PERSON>--20240830-093435.txt", "raw_config": ["interface WLAN-ESS66", "port link-type hybrid", "undo port hybrid vlan 1", "port hybrid pvid vlan 3771", "mac-vlan enable", "#"], "description": "", "port_type": "hybrid", "vlan_config": {"undo_vlan": "1", "pvid_vlan": "3771", "mac_vlan": "enable"}, "security_config": {}, "other_config": []}, "WLAN-ESS78": {"interface_name": "WLAN-ESS78", "source_file": "*************-De<PERSON><PERSON>--20240830-093435.txt", "raw_config": ["interface WLAN-ESS78", "description free", "port link-type hybrid", "undo port hybrid vlan 1", "port hybrid vlan 2 untagged", "port hybrid pvid vlan 2", "mac-vlan enable", "#"], "description": "free", "port_type": "hybrid", "vlan_config": {"undo_vlan": "1", "untagged_vlan": "2", "pvid_vlan": "2", "mac_vlan": "enable"}, "security_config": {}, "other_config": []}}, "test_config2.txt": {"WLAN-ESS1": {"interface_name": "WLAN-ESS1", "source_file": "test_config2.txt", "raw_config": ["interface WLAN-ESS1", "description business", "port link-type hybrid", "undo port hybrid vlan 1", "port hybrid vlan 3321 untagged", "port hybrid pvid vlan 3321", "mac-vlan enable", "#"], "description": "business", "port_type": "hybrid", "vlan_config": {"undo_vlan": "1", "untagged_vlan": "3321", "pvid_vlan": "3321", "mac_vlan": "enable"}, "security_config": {}, "other_config": []}, "WLAN-ESS15": {"interface_name": "WLAN-ESS15", "source_file": "test_config2.txt", "raw_config": ["interface WLAN-ESS15", "description CMCC-AUTO", "port link-type hybrid", "undo port hybrid vlan 1", "port hybrid vlan 3322 untagged", "port hybrid pvid vlan 3322", "mac-vlan enable", "port-security port-mode userlogin-secure-ext", "port-security tx-key-type 11key", "undo dot1x handshake", "dot1x mandatory-domain eap", "#"], "description": "CMCC-AUTO", "port_type": "hybrid", "vlan_config": {"undo_vlan": "1", "untagged_vlan": "3322", "pvid_vlan": "3322", "mac_vlan": "enable"}, "security_config": {"port_mode": "userlogin-secure-ext", "tx_key_type": "11key", "dot1x_handshake": "disabled", "dot1x_domain": "eap"}, "other_config": []}, "WLAN-ESS99": {"interface_name": "WLAN-ESS99", "source_file": "test_config2.txt", "raw_config": ["interface WLAN-ESS99", "description guest", "port link-type hybrid", "undo port hybrid vlan 1", "port hybrid vlan 100 untagged", "port hybrid pvid vlan 100", "mac-vlan enable", "#"], "description": "guest", "port_type": "hybrid", "vlan_config": {"undo_vlan": "1", "untagged_vlan": "100", "pvid_vlan": "100", "mac_vlan": "enable"}, "security_config": {}, "other_config": []}}}, "comparison_results": {"WLAN-ESS1": {"interface_name": "WLAN-ESS1", "files": ["*************-De<PERSON><PERSON>--20240830-093435.txt", "test_config2.txt"], "differences": {"description": {"*************-Default--20240830-093435.txt": "yewu", "test_config2.txt": "business"}}, "identical": false}, "WLAN-ESS15": {"interface_name": "WLAN-ESS15", "files": ["*************-De<PERSON><PERSON>--20240830-093435.txt", "test_config2.txt"], "differences": {"vlan_config": {"*************-Default--20240830-093435.txt": {"undo_vlan": "1", "untagged_vlan": "3321", "pvid_vlan": "3321", "mac_vlan": "enable"}, "test_config2.txt": {"undo_vlan": "1", "untagged_vlan": "3322", "pvid_vlan": "3322", "mac_vlan": "enable"}}}, "identical": false}}, "summary": {"total_files": 2, "total_interfaces": 7, "interfaces_with_differences": 2}}