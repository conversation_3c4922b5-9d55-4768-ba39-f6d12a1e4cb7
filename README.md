# WLAN-ESS接口配置提取和对比工具

这个Python脚本用于从多个txt配置文件中提取WLAN-ESS接口配置，并对相同接口名进行对比分析。

## 功能特性

- **自动提取**: 从txt文件中自动识别和提取所有WLAN-ESS接口配置
- **结构化解析**: 将配置解析为结构化数据，包括：
  - 接口描述
  - 端口类型
  - VLAN配置
  - 安全配置
  - 其他配置项
- **多文件支持**: 可以处理目录中的多个txt文件
- **配置对比**: 自动对比相同接口名在不同文件中的配置差异
- **多种输出格式**: 支持摘要、详细和仅差异显示模式
- **JSON导出**: 将结果保存为JSON格式便于后续处理

## 使用方法

### 基本用法

```bash
# 处理当前目录中的所有txt文件
python wlan_interface_extractor.py

# 处理指定目录中的txt文件
python wlan_interface_extractor.py /path/to/config/files
```

### 命令行参数

```bash
python wlan_interface_extractor.py [-h] [-o OUTPUT] [-d] [-c] [directory]
```

**参数说明:**
- `directory`: 包含txt配置文件的目录路径（默认：当前目录）
- `-o, --output`: 输出JSON文件路径（默认：wlan_interfaces_result.json）
- `-d, --detailed`: 显示详细的接口配置信息
- `-c, --compare-only`: 只显示有差异的接口对比结果
- `-h, --help`: 显示帮助信息

### 使用示例

1. **基本提取和摘要**:
   ```bash
   python wlan_interface_extractor.py
   ```

2. **显示详细配置信息**:
   ```bash
   python wlan_interface_extractor.py -d
   ```

3. **只显示有差异的接口**:
   ```bash
   python wlan_interface_extractor.py -c
   ```

4. **处理指定目录并自定义输出文件**:
   ```bash
   python wlan_interface_extractor.py /path/to/configs -o my_results.json
   ```

## 输出格式

### 控制台输出

脚本会在控制台显示：
- 处理进度信息
- 发现的接口统计
- 配置差异对比结果
- 详细配置信息（使用-d参数时）

### JSON输出文件

生成的JSON文件包含：
- `extraction_results`: 所有提取的接口配置
- `comparison_results`: 接口配置对比结果
- `summary`: 统计摘要信息

## 支持的配置项

脚本能够识别和解析以下配置项：

### 基本配置
- 接口名称（WLAN-ESS + 数字）
- 描述信息
- 端口类型

### VLAN配置
- `undo port hybrid vlan`
- `port hybrid vlan X untagged`
- `port hybrid pvid vlan X`
- `mac-vlan enable`

### 安全配置
- `port-security port-mode`
- `port-security tx-key-type`
- `undo dot1x handshake`
- `dot1x mandatory-domain`

## 配置文件格式要求

脚本支持标准的华为/H3C设备配置格式：

```
interface WLAN-ESS1
 description yewu
 port link-type hybrid
 undo port hybrid vlan 1
 port hybrid vlan 3321 untagged
 port hybrid pvid vlan 3321
 mac-vlan enable
#
```

- 接口配置以`interface WLAN-ESS`开头
- 配置项以空格或制表符缩进
- 接口配置以`#`结尾

## 依赖要求

- Python 3.6+
- 标准库模块：os, re, json, collections, typing, argparse

## 注意事项

1. 脚本会自动忽略编码错误，使用UTF-8编码读取文件
2. 只处理以`.txt`结尾的文件
3. 接口名必须符合`WLAN-ESS + 数字`的格式
4. 配置对比基于接口名称，相同名称的接口会被对比
5. 生成的JSON文件使用UTF-8编码，支持中文字符

## 示例输出

```
============================================================
WLAN-ESS接口配置提取和对比结果摘要
============================================================

处理的文件数量: 1
  config.txt: 4 个接口

发现的接口总数: 4
接口列表: ['WLAN-ESS1', 'WLAN-ESS15', 'WLAN-ESS66', 'WLAN-ESS78']

所有相同名称的接口配置都一致
```
